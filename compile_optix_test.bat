@echo off
REM compile_optix_test.bat
REM PhotonRender - Compile OptiX Linking Test
REM Test per verificare che OptiX 9.0.0 sia correttamente linkato

echo === PhotonRender OptiX Linking Test Compilation ===
echo.

REM Set environment variables
set OPTIX_ROOT=C:\ProgramData\NVIDIA Corporation\OptiX SDK 9.0.0
set CUDA_ROOT=C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.9

echo OptiX Root: %OPTIX_ROOT%
echo CUDA Root: %CUDA_ROOT%
echo.

REM Check if OptiX is installed
if not exist "%OPTIX_ROOT%" (
    echo ERROR: OptiX SDK not found at %OPTIX_ROOT%
    pause
    exit /b 1
)

REM Check if CUDA is installed
if not exist "%CUDA_ROOT%" (
    echo ERROR: CUDA Toolkit not found at %CUDA_ROOT%
    pause
    exit /b 1
)

echo ✅ OptiX SDK 9.0.0 found
echo ✅ CUDA Toolkit 12.9 found
echo.

REM Setup Visual Studio environment
echo Setting up Visual Studio 2022 environment...
call "C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Auxiliary\Build\vcvars64.bat" >nul 2>&1

if %ERRORLEVEL% neq 0 (
    echo WARNING: VS2022 Community not found, trying Professional...
    call "C:\Program Files\Microsoft Visual Studio\2022\Professional\VC\Auxiliary\Build\vcvars64.bat" >nul 2>&1
)

if %ERRORLEVEL% neq 0 (
    echo WARNING: VS2022 not found, trying Build Tools...
    call "C:\Program Files (x86)\Microsoft Visual Studio\2022\BuildTools\VC\Auxiliary\Build\vcvars64.bat" >nul 2>&1
)

if %ERRORLEVEL% neq 0 (
    echo ERROR: Visual Studio 2022 not found in any location
    pause
    exit /b 1
)

REM Compile with Visual Studio 2022
echo Compiling OptiX linking test...
echo.

cl.exe /EHsc ^
    /I"%OPTIX_ROOT%\include" ^
    /I"%CUDA_ROOT%\include" ^
    test_optix_linking.cpp ^
    /link ^
    "%CUDA_ROOT%\lib\x64\cudart.lib" ^
    "%CUDA_ROOT%\lib\x64\cuda.lib" ^
    /OUT:test_optix_linking.exe

if %ERRORLEVEL% neq 0 (
    echo.
    echo ❌ COMPILATION FAILED
    echo.
    echo Possible issues:
    echo 1. Visual Studio 2022 not in PATH
    echo 2. OptiX headers not found
    echo 3. CUDA libraries not found
    echo 4. Missing dependencies
    echo.
    pause
    exit /b 1
)

echo.
echo ✅ COMPILATION SUCCESS
echo.

REM Run the test
echo Running OptiX linking test...
echo.
test_optix_linking.exe

if %ERRORLEVEL% equ 0 (
    echo.
    echo 🎉 OptiX LINKING TEST PASSED!
    echo 🚀 Ready for 10+ Grays/sec performance
) else (
    echo.
    echo ❌ OptiX LINKING TEST FAILED
    echo Check CUDA drivers and OptiX installation
)

echo.
pause
