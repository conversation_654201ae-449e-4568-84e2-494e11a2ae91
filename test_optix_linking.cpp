// test_optix_linking.cpp
// PhotonRender - Test OptiX Linking e RT Cores Detection
// Test semplificato per verificare che OptiX 9.0.0 sia correttamente linkato

#include <iostream>
#include <cuda_runtime.h>
#include <cuda.h>

// OptiX headers
#include <optix.h>
#include <optix_stubs.h>

// Link OptiX stubs statically
#pragma comment(lib, "cuda.lib")
#pragma comment(lib, "cudart.lib")

int main() {
    std::cout << "=== PhotonRender OptiX Linking Test ===" << std::endl;
    
    // Test 1: CUDA Device Detection
    std::cout << "\n1. CUDA Device Detection:" << std::endl;
    
    int device_count = 0;
    cudaError_t cuda_result = cudaGetDeviceCount(&device_count);
    
    if (cuda_result != cudaSuccess) {
        std::cerr << "ERROR: CUDA not available: " << cudaGetErrorString(cuda_result) << std::endl;
        return 1;
    }
    
    std::cout << "   CUDA Devices Found: " << device_count << std::endl;
    
    if (device_count == 0) {
        std::cerr << "ERROR: No CUDA devices found" << std::endl;
        return 1;
    }
    
    // Get device properties
    cudaDeviceProp prop;
    cudaGetDeviceProperties(&prop, 0);
    
    std::cout << "   Device Name: " << prop.name << std::endl;
    std::cout << "   Compute Capability: " << prop.major << "." << prop.minor << std::endl;
    std::cout << "   Multiprocessors: " << prop.multiProcessorCount << std::endl;
    std::cout << "   Memory: " << (prop.totalGlobalMem / (1024*1024)) << " MB" << std::endl;
    
    // Check if device supports RT Cores (Turing+ architecture)
    bool has_rt_cores = prop.major >= 7; // Turing (7.x) or newer
    std::cout << "   RT Cores Support: " << (has_rt_cores ? "YES" : "NO") << std::endl;
    
    if (!has_rt_cores) {
        std::cerr << "WARNING: Device does not support RT Cores" << std::endl;
    }
    
    // Test 2: OptiX Version Detection
    std::cout << "\n2. OptiX Version Detection:" << std::endl;
    
    // Initialize OptiX
    OptixResult optix_result = optixInit();
    if (optix_result != OPTIX_SUCCESS) {
        std::cerr << "ERROR: OptiX initialization failed: " << optix_result << std::endl;
        return 1;
    }
    
    std::cout << "   OptiX Initialization: SUCCESS" << std::endl;
    
    // Get OptiX version
    unsigned int optix_version;
    optix_result = optixGetVersion(&optix_version);
    if (optix_result != OPTIX_SUCCESS) {
        std::cerr << "ERROR: Failed to get OptiX version: " << optix_result << std::endl;
        return 1;
    }
    
    int major = optix_version / 10000;
    int minor = (optix_version % 10000) / 100;
    int micro = optix_version % 100;
    
    std::cout << "   OptiX Version: " << major << "." << minor << "." << micro << std::endl;
    
    // Test 3: OptiX Context Creation
    std::cout << "\n3. OptiX Context Creation:" << std::endl;
    
    // Initialize CUDA context
    cudaError_t cuda_init = cudaFree(0);
    if (cuda_init != cudaSuccess) {
        std::cerr << "ERROR: CUDA context initialization failed" << std::endl;
        return 1;
    }
    
    // Get CUDA context
    CUcontext cuda_context;
    CUresult cu_result = cuCtxGetCurrent(&cuda_context);
    if (cu_result != CUDA_SUCCESS) {
        std::cerr << "ERROR: Failed to get CUDA context" << std::endl;
        return 1;
    }
    
    // Create OptiX device context
    OptixDeviceContext optix_context;
    OptixDeviceContextOptions options = {};
    
    optix_result = optixDeviceContextCreate(cuda_context, &options, &optix_context);
    if (optix_result != OPTIX_SUCCESS) {
        std::cerr << "ERROR: OptiX context creation failed: " << optix_result << std::endl;
        return 1;
    }
    
    std::cout << "   OptiX Context Creation: SUCCESS" << std::endl;
    
    // Test 4: Memory Information
    std::cout << "\n4. GPU Memory Information:" << std::endl;
    
    size_t free_mem, total_mem;
    cudaMemGetInfo(&free_mem, &total_mem);
    
    std::cout << "   Total Memory: " << (total_mem / (1024*1024)) << " MB" << std::endl;
    std::cout << "   Free Memory: " << (free_mem / (1024*1024)) << " MB" << std::endl;
    std::cout << "   Used Memory: " << ((total_mem - free_mem) / (1024*1024)) << " MB" << std::endl;
    
    // Test 5: RT Cores Estimation
    std::cout << "\n5. RT Cores Estimation:" << std::endl;
    
    int estimated_rt_cores = 0;
    if (has_rt_cores) {
        // For RTX 4070: typically 1 RT Core per SM
        estimated_rt_cores = prop.multiProcessorCount;
        std::cout << "   Estimated RT Cores: " << estimated_rt_cores << std::endl;
        std::cout << "   Architecture: Ada Lovelace (RTX 40xx)" << std::endl;
    } else {
        std::cout << "   RT Cores: Not Available" << std::endl;
    }
    
    // Cleanup
    if (optix_context) {
        optixDeviceContextDestroy(optix_context);
    }
    
    // Test Results Summary
    std::cout << "\n=== Test Results Summary ===" << std::endl;
    std::cout << "✅ CUDA: " << (cuda_result == cudaSuccess ? "PASS" : "FAIL") << std::endl;
    std::cout << "✅ OptiX: " << (optix_result == OPTIX_SUCCESS ? "PASS" : "FAIL") << std::endl;
    std::cout << "✅ RT Cores: " << (has_rt_cores ? "AVAILABLE" : "NOT AVAILABLE") << std::endl;
    std::cout << "✅ Context: " << (optix_context ? "CREATED" : "FAILED") << std::endl;
    
    if (cuda_result == cudaSuccess && optix_result == OPTIX_SUCCESS && has_rt_cores) {
        std::cout << "\n🎉 SUCCESS: OptiX 9.0.0 ready for hardware ray tracing!" << std::endl;
        std::cout << "🚀 Target Performance: 10+ Grays/sec with " << estimated_rt_cores << " RT Cores" << std::endl;
        return 0;
    } else {
        std::cout << "\n❌ FAILURE: OptiX setup incomplete" << std::endl;
        return 1;
    }
}
