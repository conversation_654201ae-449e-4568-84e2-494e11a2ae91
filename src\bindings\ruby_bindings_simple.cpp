// src/bindings/ruby_bindings_simple.cpp
// PhotonRender - Ruby-C++ Bindings Simplified
// Test semplificato per verificare bridge SketchUp ↔ PhotonRender

#include <ruby.h>
#include <iostream>
#include <string>
#include <vector>

// Namespace per evitare conflitti
namespace photon_ruby_simple {

// Forward declarations
static VALUE rb_mPhotonCore;
static VALUE rb_cRender;

// PhotonCore.initialize
static VALUE photon_core_initialize(VALUE self) {
    std::cout << "PhotonRender Core initialized!" << std::endl;
    return Qtrue;
}

// PhotonCore.shutdown
static VALUE photon_core_shutdown(VALUE self) {
    std::cout << "PhotonRender Core shutdown!" << std::endl;
    return Qnil;
}

// PhotonCore.version
static VALUE photon_core_version(VALUE self) {
    return rb_str_new_cstr("3.0.0-alpha");
}

// PhotonCore.has_cuda_device?
static VALUE photon_core_has_cuda(VALUE self) {
    return Qtrue; // Sappiamo che CUDA è disponibile
}

// PhotonCore.has_optix_device?
static VALUE photon_core_has_optix(VALUE self) {
    return Qtrue; // Sappiamo che OptiX è disponibile
}

// PhotonCore.get_capabilities
static VALUE photon_core_capabilities(VALUE self) {
    VALUE hash = rb_hash_new();
    rb_hash_aset(hash, rb_str_new_cstr("embree"), Qtrue);
    rb_hash_aset(hash, rb_str_new_cstr("cuda"), Qtrue);
    rb_hash_aset(hash, rb_str_new_cstr("optix"), Qtrue);
    rb_hash_aset(hash, rb_str_new_cstr("openmp"), Qtrue);
    rb_hash_aset(hash, rb_str_new_cstr("max_threads"), INT2NUM(8));
    rb_hash_aset(hash, rb_str_new_cstr("gpu_count"), INT2NUM(1));
    rb_hash_aset(hash, rb_str_new_cstr("rt_cores"), INT2NUM(36));
    
    return hash;
}

// PhotonCore.test_connection
static VALUE photon_core_test_connection(VALUE self) {
    std::cout << "Ruby-C++ bridge working correctly!" << std::endl;
    return rb_str_new_cstr("Bridge connection successful");
}

// Wrapper per Renderer semplificato
struct SimpleRendererWrapper {
    int width;
    int height;
    int samples;
    bool is_rendering;
    float progress;
    
    SimpleRendererWrapper() : width(512), height(512), samples(16), is_rendering(false), progress(0.0f) {}
};

// Cleanup function per Ruby GC
static void simple_renderer_free(void* ptr) {
    if (ptr) {
        delete static_cast<SimpleRendererWrapper*>(ptr);
    }
}

// Allocator per Ruby objects
static VALUE simple_renderer_alloc(VALUE klass) {
    SimpleRendererWrapper* wrapper = new SimpleRendererWrapper();
    return Data_Wrap_Struct(klass, nullptr, simple_renderer_free, wrapper);
}

// Render.new
static VALUE render_initialize(VALUE self) {
    std::cout << "PhotonRender Renderer created!" << std::endl;
    return self;
}

// Render#set_settings(settings_hash)
static VALUE render_set_settings(VALUE self, VALUE settings) {
    SimpleRendererWrapper* wrapper;
    Data_Get_Struct(self, SimpleRendererWrapper, wrapper);
    
    // Estrai settings da hash Ruby
    if (TYPE(settings) == T_HASH) {
        VALUE width = rb_hash_aref(settings, rb_str_new_cstr("width"));
        VALUE height = rb_hash_aref(settings, rb_str_new_cstr("height"));
        VALUE samples = rb_hash_aref(settings, rb_str_new_cstr("samples_per_pixel"));
        
        if (!NIL_P(width)) wrapper->width = NUM2INT(width);
        if (!NIL_P(height)) wrapper->height = NUM2INT(height);
        if (!NIL_P(samples)) wrapper->samples = NUM2INT(samples);
        
        std::cout << "Render settings: " << wrapper->width << "x" << wrapper->height 
                  << " @ " << wrapper->samples << " SPP" << std::endl;
    }
    
    return Qnil;
}

// Render#render
static VALUE render_start(VALUE self) {
    SimpleRendererWrapper* wrapper;
    Data_Get_Struct(self, SimpleRendererWrapper, wrapper);
    
    std::cout << "Starting render: " << wrapper->width << "x" << wrapper->height 
              << " @ " << wrapper->samples << " SPP" << std::endl;
    
    wrapper->is_rendering = true;
    wrapper->progress = 0.0f;
    
    // Simula rendering
    for (int i = 0; i <= 100; i += 10) {
        wrapper->progress = i / 100.0f;
        std::cout << "Render progress: " << i << "%" << std::endl;
    }
    
    wrapper->is_rendering = false;
    wrapper->progress = 1.0f;
    
    std::cout << "Render completed!" << std::endl;
    return Qtrue;
}

// Render#stop
static VALUE render_stop(VALUE self) {
    SimpleRendererWrapper* wrapper;
    Data_Get_Struct(self, SimpleRendererWrapper, wrapper);
    
    wrapper->is_rendering = false;
    std::cout << "Render stopped!" << std::endl;
    return Qnil;
}

// Render#progress
static VALUE render_progress(VALUE self) {
    SimpleRendererWrapper* wrapper;
    Data_Get_Struct(self, SimpleRendererWrapper, wrapper);
    
    return rb_float_new(wrapper->progress);
}

// Render#is_complete?
static VALUE render_is_complete(VALUE self) {
    SimpleRendererWrapper* wrapper;
    Data_Get_Struct(self, SimpleRendererWrapper, wrapper);
    
    return wrapper->is_rendering ? Qfalse : Qtrue;
}

// Render#width
static VALUE render_width(VALUE self) {
    SimpleRendererWrapper* wrapper;
    Data_Get_Struct(self, SimpleRendererWrapper, wrapper);
    
    return INT2NUM(wrapper->width);
}

// Render#height
static VALUE render_height(VALUE self) {
    SimpleRendererWrapper* wrapper;
    Data_Get_Struct(self, SimpleRendererWrapper, wrapper);
    
    return INT2NUM(wrapper->height);
}

// PhotonCore.save_test_image(filename, width, height)
static VALUE photon_core_save_test_image(VALUE self, VALUE filename, VALUE width, VALUE height) {
    const char* fname = StringValueCStr(filename);
    int w = NUM2INT(width);
    int h = NUM2INT(height);
    
    std::cout << "Saving test image: " << fname << " (" << w << "x" << h << ")" << std::endl;
    return Qtrue;
}

} // namespace photon_ruby_simple

// Entry point per Ruby extension
extern "C" {

void Init_photon_core_simple() {
    using namespace photon_ruby_simple;
    
    // Modulo principale PhotonCore
    rb_mPhotonCore = rb_define_module("PhotonCore");
    
    // Metodi del modulo
    rb_define_singleton_method(rb_mPhotonCore, "initialize", RUBY_METHOD_FUNC(photon_core_initialize), 0);
    rb_define_singleton_method(rb_mPhotonCore, "shutdown", RUBY_METHOD_FUNC(photon_core_shutdown), 0);
    rb_define_singleton_method(rb_mPhotonCore, "version", RUBY_METHOD_FUNC(photon_core_version), 0);
    rb_define_singleton_method(rb_mPhotonCore, "has_cuda_device?", RUBY_METHOD_FUNC(photon_core_has_cuda), 0);
    rb_define_singleton_method(rb_mPhotonCore, "has_optix_device?", RUBY_METHOD_FUNC(photon_core_has_optix), 0);
    rb_define_singleton_method(rb_mPhotonCore, "capabilities", RUBY_METHOD_FUNC(photon_core_capabilities), 0);
    rb_define_singleton_method(rb_mPhotonCore, "test_connection", RUBY_METHOD_FUNC(photon_core_test_connection), 0);
    rb_define_singleton_method(rb_mPhotonCore, "save_test_image", RUBY_METHOD_FUNC(photon_core_save_test_image), 3);
    
    // Classe Render
    rb_cRender = rb_define_class_under(rb_mPhotonCore, "Render", rb_cObject);
    rb_define_alloc_func(rb_cRender, simple_renderer_alloc);
    rb_define_method(rb_cRender, "initialize", RUBY_METHOD_FUNC(render_initialize), 0);
    rb_define_method(rb_cRender, "set_settings", RUBY_METHOD_FUNC(render_set_settings), 1);
    rb_define_method(rb_cRender, "render", RUBY_METHOD_FUNC(render_start), 0);
    rb_define_method(rb_cRender, "stop", RUBY_METHOD_FUNC(render_stop), 0);
    rb_define_method(rb_cRender, "progress", RUBY_METHOD_FUNC(render_progress), 0);
    rb_define_method(rb_cRender, "is_complete?", RUBY_METHOD_FUNC(render_is_complete), 0);
    rb_define_method(rb_cRender, "width", RUBY_METHOD_FUNC(render_width), 0);
    rb_define_method(rb_cRender, "height", RUBY_METHOD_FUNC(render_height), 0);
    
    std::cout << "PhotonRender Ruby Extension Loaded Successfully!" << std::endl;
}

} // extern "C"
