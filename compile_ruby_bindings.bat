@echo off
REM compile_ruby_bindings.bat
REM PhotonRender - Compile Ruby Bindings Test
REM Test semplificato per verificare bridge Ruby-C++

echo === PhotonRender Ruby Bindings Compilation ===
echo.

REM Set environment variables
set RUBY_ROOT=C:\Ruby34-x64
set RUBY_INCLUDE_DIR=%RUBY_ROOT%\include\ruby-3.4.0
set RUBY_ARCH_DIR=%RUBY_INCLUDE_DIR%\x64-mingw-ucrt
set RUBY_LIBRARY=%RUBY_ROOT%\lib\libx64-ucrt-ruby340.dll.a

echo Ruby Root: %RUBY_ROOT%
echo Ruby Include: %RUBY_INCLUDE_DIR%
echo Ruby Arch: %RUBY_ARCH_DIR%
echo Ruby Library: %RUBY_LIBRARY%
echo.

REM Check if Ruby is installed
if not exist "%RUBY_INCLUDE_DIR%" (
    echo ERROR: Ruby headers not found at %RUBY_INCLUDE_DIR%
    pause
    exit /b 1
)

if not exist "%RUBY_LIBRARY%" (
    echo ERROR: Ruby library not found at %RUBY_LIBRARY%
    pause
    exit /b 1
)

echo ✅ Ruby 3.4.0 development files found
echo.

REM Setup Visual Studio environment
echo Setting up Visual Studio 2022 environment...
call "C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Auxiliary\Build\vcvars64.bat" >nul 2>&1

if %ERRORLEVEL% neq 0 (
    echo WARNING: VS2022 Community not found, trying Professional...
    call "C:\Program Files\Microsoft Visual Studio\2022\Professional\VC\Auxiliary\Build\vcvars64.bat" >nul 2>&1
)

if %ERRORLEVEL% neq 0 (
    echo WARNING: VS2022 not found, trying Build Tools...
    call "C:\Program Files (x86)\Microsoft Visual Studio\2022\BuildTools\VC\Auxiliary\Build\vcvars64.bat" >nul 2>&1
)

if %ERRORLEVEL% neq 0 (
    echo ERROR: Visual Studio 2022 not found in any location
    pause
    exit /b 1
)

REM Compile Ruby extension
echo Compiling Ruby bindings...
echo.

cl.exe /EHsc /LD ^
    /I"%RUBY_INCLUDE_DIR%" ^
    /I"%RUBY_ARCH_DIR%" ^
    /DRUBY_EXTCONF_H ^
    /D_CRT_SECURE_NO_WARNINGS ^
    src\bindings\ruby_bindings_simple.cpp ^
    /link ^
    "%RUBY_LIBRARY%" ^
    /OUT:photon_core_simple.so

if %ERRORLEVEL% neq 0 (
    echo.
    echo ❌ COMPILATION FAILED
    echo.
    echo Possible issues:
    echo 1. Visual Studio 2022 not properly configured
    echo 2. Ruby headers not found
    echo 3. Ruby library not found
    echo 4. Missing dependencies
    echo.
    pause
    exit /b 1
)

echo.
echo ✅ COMPILATION SUCCESS
echo.

REM Check if the .so file was created
if exist "photon_core_simple.so" (
    echo ✅ Ruby extension created: photon_core_simple.so
    dir photon_core_simple.so
) else (
    echo ❌ Ruby extension not found
    exit /b 1
)

echo.
echo 🎉 Ruby Bindings Compilation Completed!
echo 📋 Next: Test Ruby extension loading

echo.
pause
