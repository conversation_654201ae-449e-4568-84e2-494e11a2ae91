@echo off
REM compile_ruby_extension.bat
REM PhotonRender - Compile Ruby Extension with mkmf
REM Usa il sistema Ruby standard per compilare l'extension

echo === PhotonRender Ruby Extension Compilation ===
echo.

REM Setup Visual Studio environment
echo Setting up Visual Studio 2022 environment...
call "C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Auxiliary\Build\vcvars64.bat" >nul 2>&1

if %ERRORLEVEL% neq 0 (
    echo WARNING: VS2022 Community not found, trying Professional...
    call "C:\Program Files\Microsoft Visual Studio\2022\Professional\VC\Auxiliary\Build\vcvars64.bat" >nul 2>&1
)

if %ERRORLEVEL% neq 0 (
    echo WARNING: VS2022 not found, trying Build Tools...
    call "C:\Program Files (x86)\Microsoft Visual Studio\2022\BuildTools\VC\Auxiliary\Build\vcvars64.bat" >nul 2>&1
)

if %ERRORLEVEL% neq 0 (
    echo ERROR: Visual Studio 2022 not found in any location
    pause
    exit /b 1
)

echo ✅ Visual Studio environment configured
echo.

REM Verifica che extconf.rb sia stato eseguito
if not exist "Makefile" (
    echo Running Ruby extconf.rb...
    ruby extconf.rb
    if %ERRORLEVEL% neq 0 (
        echo ERROR: extconf.rb failed
        pause
        exit /b 1
    )
)

echo ✅ Makefile found
echo.

REM Compila con nmake
echo Compiling Ruby extension with nmake...
echo.

nmake

if %ERRORLEVEL% neq 0 (
    echo.
    echo ❌ COMPILATION FAILED
    echo.
    echo Possible issues:
    echo 1. Visual Studio environment not properly set
    echo 2. Ruby development files missing
    echo 3. Source file compilation errors
    echo.
    pause
    exit /b 1
)

echo.
echo ✅ COMPILATION SUCCESS
echo.

REM Verifica che l'extension sia stata creata
if exist "photon_core_simple.so" (
    echo ✅ Ruby extension created: photon_core_simple.so
    dir photon_core_simple.so
) else (
    echo ❌ Ruby extension not found
    exit /b 1
)

echo.
echo 🎉 Ruby Extension Compilation Completed!
echo 📋 Next: Test Ruby extension loading

echo.
pause
