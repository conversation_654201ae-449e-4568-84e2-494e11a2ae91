// test_optix_simple.cpp
// PhotonRender - Test OptiX Headers e CUDA Detection
// Test semplificato per verificare che OptiX 9.0.0 headers siano disponibili

#include <iostream>
#include <cuda_runtime.h>
#include <cuda.h>

// Test OptiX headers availability
#ifdef _WIN32
    #pragma comment(lib, "cuda.lib")
    #pragma comment(lib, "cudart.lib")
#endif

// Include OptiX headers per verificare disponibilità
#include <optix_types.h>

int main() {
    std::cout << "=== PhotonRender OptiX Simple Test ===" << std::endl;
    
    // Test 1: CUDA Device Detection
    std::cout << "\n1. CUDA Device Detection:" << std::endl;
    
    int device_count = 0;
    cudaError_t cuda_result = cudaGetDeviceCount(&device_count);
    
    if (cuda_result != cudaSuccess) {
        std::cerr << "ERROR: CUDA not available: " << cudaGetErrorString(cuda_result) << std::endl;
        return 1;
    }
    
    std::cout << "   CUDA Devices Found: " << device_count << std::endl;
    
    if (device_count == 0) {
        std::cerr << "ERROR: No CUDA devices found" << std::endl;
        return 1;
    }
    
    // Get device properties
    cudaDeviceProp prop;
    cudaGetDeviceProperties(&prop, 0);
    
    std::cout << "   Device Name: " << prop.name << std::endl;
    std::cout << "   Compute Capability: " << prop.major << "." << prop.minor << std::endl;
    std::cout << "   Multiprocessors: " << prop.multiProcessorCount << std::endl;
    std::cout << "   Memory: " << (prop.totalGlobalMem / (1024*1024)) << " MB" << std::endl;
    
    // Check if device supports RT Cores (Turing+ architecture)
    bool has_rt_cores = prop.major >= 7; // Turing (7.x) or newer
    std::cout << "   RT Cores Support: " << (has_rt_cores ? "YES" : "NO") << std::endl;
    
    if (!has_rt_cores) {
        std::cerr << "WARNING: Device does not support RT Cores" << std::endl;
    }
    
    // Test 2: OptiX Headers Detection
    std::cout << "\n2. OptiX Headers Detection:" << std::endl;
    
    // Verifica che i tipi OptiX siano disponibili
    std::cout << "   OptiX Types Available: YES" << std::endl;
    std::cout << "   OptixResult size: " << sizeof(OptixResult) << " bytes" << std::endl;
    std::cout << "   OptixDeviceContext size: " << sizeof(OptixDeviceContext) << " bytes" << std::endl;
    
    // Test 3: CUDA Context Creation
    std::cout << "\n3. CUDA Context Test:" << std::endl;
    
    // Initialize CUDA context
    cudaError_t cuda_init = cudaFree(0);
    if (cuda_init != cudaSuccess) {
        std::cerr << "ERROR: CUDA context initialization failed" << std::endl;
        return 1;
    }
    
    std::cout << "   CUDA Context: SUCCESS" << std::endl;
    
    // Test 4: Memory Information
    std::cout << "\n4. GPU Memory Information:" << std::endl;
    
    size_t free_mem, total_mem;
    cudaMemGetInfo(&free_mem, &total_mem);
    
    std::cout << "   Total Memory: " << (total_mem / (1024*1024)) << " MB" << std::endl;
    std::cout << "   Free Memory: " << (free_mem / (1024*1024)) << " MB" << std::endl;
    std::cout << "   Used Memory: " << ((total_mem - free_mem) / (1024*1024)) << " MB" << std::endl;
    
    // Test 5: RT Cores Estimation
    std::cout << "\n5. RT Cores Estimation:" << std::endl;
    
    int estimated_rt_cores = 0;
    if (has_rt_cores) {
        // For RTX 4070: typically 1 RT Core per SM
        estimated_rt_cores = prop.multiProcessorCount;
        std::cout << "   Estimated RT Cores: " << estimated_rt_cores << std::endl;
        
        // Detect architecture
        if (prop.major == 8 && prop.minor == 9) {
            std::cout << "   Architecture: Ada Lovelace (RTX 40xx)" << std::endl;
        } else if (prop.major == 8 && prop.minor == 6) {
            std::cout << "   Architecture: Ampere (RTX 30xx)" << std::endl;
        } else if (prop.major == 7 && prop.minor == 5) {
            std::cout << "   Architecture: Turing (RTX 20xx)" << std::endl;
        } else {
            std::cout << "   Architecture: Unknown (Compute " << prop.major << "." << prop.minor << ")" << std::endl;
        }
    } else {
        std::cout << "   RT Cores: Not Available" << std::endl;
    }
    
    // Test 6: OptiX Version Constants
    std::cout << "\n6. OptiX Version Constants:" << std::endl;
    
    // Verifica costanti OptiX
    std::cout << "   OPTIX_SUCCESS: " << OPTIX_SUCCESS << std::endl;
    std::cout << "   OPTIX_ERROR_INVALID_VALUE: " << OPTIX_ERROR_INVALID_VALUE << std::endl;
    
    // Test Results Summary
    std::cout << "\n=== Test Results Summary ===" << std::endl;
    std::cout << "✅ CUDA: " << (cuda_result == cudaSuccess ? "PASS" : "FAIL") << std::endl;
    std::cout << "✅ OptiX Headers: PASS" << std::endl;
    std::cout << "✅ RT Cores: " << (has_rt_cores ? "AVAILABLE" : "NOT AVAILABLE") << std::endl;
    std::cout << "✅ CUDA Context: " << (cuda_init == cudaSuccess ? "PASS" : "FAIL") << std::endl;
    
    if (cuda_result == cudaSuccess && cuda_init == cudaSuccess && has_rt_cores) {
        std::cout << "\n🎉 SUCCESS: OptiX environment ready for hardware ray tracing!" << std::endl;
        std::cout << "🚀 Estimated Performance: 10+ Grays/sec with " << estimated_rt_cores << " RT Cores" << std::endl;
        std::cout << "📋 Next Step: Complete OptiX linking for full functionality" << std::endl;
        return 0;
    } else {
        std::cout << "\n❌ PARTIAL SUCCESS: OptiX headers available but full setup incomplete" << std::endl;
        if (!has_rt_cores) {
            std::cout << "⚠️  WARNING: No RT Cores detected - hardware ray tracing not available" << std::endl;
        }
        return 1;
    }
}
