# extconf.rb
# PhotonRender - Ruby Extension Configuration
# Configurazione automatica per Ruby-C++ bindings

require 'mkmf'

# Nome dell'extension
extension_name = 'photon_core_simple'

# Verifica che Ruby sia configurato correttamente
puts "Ruby version: #{RUBY_VERSION}"
puts "Ruby platform: #{RUBY_PLATFORM}"
puts "Ruby include dir: #{RbConfig::CONFIG['rubyhdrdir']}"
puts "Ruby library dir: #{RbConfig::CONFIG['libdir']}"

# Configura directories
$INCFLAGS << " -I#{File.expand_path('src')}"
$INCFLAGS << " -I#{File.expand_path('include')}"

# Configura flags di compilazione
$CPPFLAGS += " -DRUBY_EXTCONF_H"
$CPPFLAGS += " -D_CRT_SECURE_NO_WARNINGS" if RUBY_PLATFORM =~ /mswin|mingw/

# Verifica che i file sorgenti esistano
source_file = 'src/bindings/ruby_bindings_simple.cpp'
unless File.exist?(source_file)
  puts "ERROR: Source file not found: #{source_file}"
  exit 1
end

puts "Source file found: #{source_file}"

# Crea il Makefile
create_makefile(extension_name, 'src/bindings')

puts "Makefile created successfully!"
puts "Run 'make' to compile the extension"
